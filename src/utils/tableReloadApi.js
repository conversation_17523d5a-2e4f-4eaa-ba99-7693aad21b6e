import axios from 'axios'
import { handleError } from './errorHandler'

/**
 * Reload table data using the backend /reload_data API endpoint
 * @param {string} table_id - Table ID to reload data for
 * @returns {Promise<Object>} Fresh table data structure
 */
export const reloadTableData = async (table_id) => {
  try {
    console.log('Reloading table data for table_id:', table_id)
    
    if (!table_id) {
      throw new Error('Table ID is required for data reload')
    }

    const response = await axios.post('/api/v1/sessions/reload_data', {
      table_id: table_id
    }, {
      headers: {
        'Content-Type': 'application/json',
        token: localStorage.getItem('token'),
      },
    })
    
    console.log('Table reload API response:', response.data)

    // Handle successful response - check for proper API response structure
    if (response.data && response.status === 200) {
      // Check for API error code
      if (response.data.error_code !== 0) {
        throw new Error(response.data.message || 'API returned error')
      }

      // Return the nested data object that contains table_data, table_info, etc.
      if (response.data.data) {
        return response.data.data
      } else {
        throw new Error('Missing data in API response')
      }
    } else {
      throw new Error('Invalid response from reload API')
    }
  } catch (error) {
    console.error('Failed to reload table data:', error)
    
    // Handle specific error cases
    if (error.response) {
      const status = error.response.status
      const data = error.response.data
      
      let errorMessage = '表格数据重新加载失败'
      
      switch (status) {
        case 400:
          errorMessage = '无效的表格ID，请检查表格是否存在'
          break
        case 401:
          errorMessage = '认证失败，请重新登录'
          break
        case 404:
          errorMessage = '表格不存在或已被删除'
          break
        case 500:
          errorMessage = '服务器内部错误，请稍后重试'
          break
        default:
          errorMessage = data?.message || `服务器错误 (${status})`
      }
      
      // Use error handler for consistent error management
      const retryAction = () => reloadTableData(table_id)
      handleError(error, errorMessage, retryAction)
      
      throw new Error(errorMessage)
    } else if (error.request) {
      // Network error
      const errorMessage = '网络连接失败，请检查网络连接'
      const retryAction = () => reloadTableData(table_id)
      handleError(error, errorMessage, retryAction)
      
      throw new Error(errorMessage)
    } else {
      // Other errors
      const errorMessage = error.message || '未知错误'
      throw new Error(errorMessage)
    }
  }
}

/**
 * Transform reloaded table data from API response to internal format
 * @param {Object} apiResponse - Data object from reload_data endpoint (already extracted from response.data.data)
 * @param {string} table_id - Table ID for reference
 * @returns {Object} Transformed table data in internal format
 */
export const transformReloadedData = (apiResponse, table_id) => {
  try {
    console.log('Transforming reload data:', { apiResponse, table_id })

    // Extract data from API response
    const { table_info, table_data, table_id: response_table_id } = apiResponse || {}

    if (!table_data || typeof table_data !== 'object') {
      console.error('Invalid table_data in API response:', { table_data, apiResponse })
      throw new Error('Invalid table data in API response')
    }

    // Use table_id from response if available, otherwise use the provided one
    const finalTableId = response_table_id || table_id
    
    // Build columns from table_info metadata (similar to existing pattern)
    let columns = []
    if (table_info && typeof table_info === 'object') {
      columns = Object.keys(table_info).map((columnName) => {
        const columnMeta = table_info[columnName] || {}
        return {
          field: columnName,
          title: columnName,
          dtype: columnMeta.dtype || 'unknown',
          ftype: columnMeta.ftype || 'unknown',
          missing_count: columnMeta.missing_count || 0,
          outlier_count: columnMeta.outlier_count || 0,
          unique_values: columnMeta.unique_values || [],
        }
      })
    } else {
      // Fallback: infer columns from table_data keys
      columns = Object.keys(table_data).map((key) => ({
        field: key,
        title: key,
        dtype: 'unknown',
        ftype: 'unknown',
        missing_count: 0,
        outlier_count: 0,
        unique_values: [],
      }))
    }
    
    // Transform table_data from column-based to row-based format
    const columnNames = Object.keys(table_data)
    const rowCount = columnNames.length > 0 ? table_data[columnNames[0]]?.length || 0 : 0
    const rows = []
    
    for (let i = 0; i < rowCount; i++) {
      const row = {}
      columnNames.forEach((columnName) => {
        const value = table_data[columnName]?.[i]
        row[columnName] = value !== undefined ? value : null
      })
      rows.push(row)
    }
    
    const result = {
      columns,
      rows,
      table_id: finalTableId,
      table_info,
      table_data,
      reloadTimestamp: new Date().toISOString(),
      source: 'api_reload', // Mark as reloaded data
    }

    console.log('Transformed reload data result:', {
      columnsCount: columns.length,
      rowsCount: rows.length,
      table_id: finalTableId
    })

    return result
  } catch (error) {
    console.error('Failed to transform reloaded data:', error)
    throw new Error(`数据转换失败: ${error.message}`)
  }
}
